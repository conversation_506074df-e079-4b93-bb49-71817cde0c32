@extends('layouts/layoutMaster')

@section('title', '<PERSON><PERSON><PERSON> c<PERSON>u chuyển kho')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-requests.js'])
@endsection

@section('content')
<!-- Transfer Requests List Table -->
<div class="card">
  <div class="card-header border-bottom">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0">Danh sách yêu cầu chuyển kho</h5>
      @can('transfer-requests.create')
        <a href="{{ route('transfer-requests.create') }}" class="btn btn-primary">
          <i class="ri-add-line ri-16px me-2"></i>
          Tạo yêu cầu chuyển kho
        </a>
      @endcan
    </div>
    <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
      <div class="col-md-4 col-12">
        <select id="status-filter" class="form-select text-capitalize">
          <option value="">Tất cả trạng thái</option>
          <option value="draft">Nháp</option>
          <option value="pending">Chờ duyệt</option>
          <option value="approved">Đã duyệt</option>
          <option value="rejected">Từ chối</option>
          <option value="cancelled">Đã hủy</option>
        </select>
      </div>
      <div class="col-md-4 col-12">
        <select id="from-warehouse-filter" class="form-select">
          <option value="">Tất cả kho nguồn</option>
          @foreach($warehouses as $warehouse)
            <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
          @endforeach
        </select>
      </div>
      <div class="col-md-4 col-12">
        <select id="to-warehouse-filter" class="form-select">
          <option value="">Tất cả kho đích</option>
          @foreach($warehouses as $warehouse)
            <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
          @endforeach
        </select>
      </div>
    </div>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-transfer-requests table">
      <thead class="table-light">
        <tr>
          <th></th>
          <th>Mã yêu cầu</th>
          <th>Kho nguồn</th>
          <th>Kho đích</th>
          <th>Trạng thái</th>
          <th>Người tạo</th>
          <th>Ngày tạo</th>
          <th>Thao tác</th>
        </tr>
      </thead>
    </table>
  </div>
</div>



<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Từ chối yêu cầu chuyển kho</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="rejectForm">
        <div class="modal-body">
          <div class="mb-3">
            <label for="reject-reason" class="form-label">Lý do từ chối <span class="text-danger">*</span></label>
            <textarea class="form-control" id="reject-reason" name="reason" rows="3" placeholder="Nhập lý do từ chối..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-danger">Từ chối</button>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection
